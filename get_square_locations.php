<?php
/**
 * Helper script to get Square location IDs for your account
 * Run this script to see all available locations for your Square account
 */

require_once 'vendor/autoload.php';

use Square\SquareClient;

// Configuration - UPDATE THESE VALUES
$accessToken = 'EAAAlxGd5HK9UNfpEUFgJXZ0bv8umrbCVkcGvDCUZVdX0Bk8EL54QIt4wFBksVCO';  // Replace with your actual access token
$environment = 'sandbox'; // Change to 'production' for live account

echo "=== Square Location Finder ===\n";
echo "Environment: " . $environment . "\n";
echo "Access Token: " . substr($accessToken, 0, 10) . "...\n\n";

try {
    $client = new SquareClient([
        'accessToken' => $accessToken,
        'environment' => $environment
    ]);

    $locationsApi = $client->getLocationsApi();
    $response = $locationsApi->listLocations();

    if ($response->isSuccess()) {
        $locations = $response->getResult()->getLocations();
        
        if (empty($locations)) {
            echo "No locations found for this account.\n";
        } else {
            echo "Found " . count($locations) . " location(s):\n\n";
            
            foreach ($locations as $location) {
                echo "Location ID: " . $location->getId() . "\n";
                echo "Name: " . $location->getName() . "\n";
                echo "Status: " . $location->getStatus() . "\n";
                echo "Type: " . $location->getType() . "\n";
                echo "Address: " . $location->getAddress()->getAddressLine1() . "\n";
                echo "City: " . $location->getAddress()->getLocality() . "\n";
                echo "Country: " . $location->getAddress()->getCountry() . "\n";
                echo "---\n";
            }
        }
    } else {
        echo "Error fetching locations:\n";
        $errors = $response->getErrors();
        foreach ($errors as $error) {
            echo "- " . $error->getCategory() . ": " . $error->getCode() . " - " . $error->getDetail() . "\n";
        }
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}

echo "\nInstructions:\n";
echo "1. Copy one of the Location IDs above\n";
echo "2. Update your Square payment configuration with the correct Location ID\n";
echo "3. Make sure you're using the right environment (sandbox vs production)\n";
?>
