<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '58161a978d8d1923fc55bd03a1e9d5288a9b98f8',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '58161a978d8d1923fc55bd03a1e9d5288a9b98f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/core' => array(
            'pretty_version' => '0.3.14',
            'version' => '0.3.14.0',
            'reference' => 'c3eaad6cf0c00b793ce6d9bee8b87176247da582',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/core-interfaces' => array(
            'pretty_version' => '0.1.5',
            'version' => '0.1.5.0',
            'reference' => 'b4f1bffc8be79584836f70af33c65e097eec155c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/core-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/jsonmapper' => array(
            'pretty_version' => '3.1.6',
            'version' => '3.1.6.0',
            'reference' => 'c6cc21bd56bfe5d5822bbd08f514be465c0b24e7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/jsonmapper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'apimatic/unirest-php' => array(
            'pretty_version' => '4.0.7',
            'version' => '4.0.7.0',
            'reference' => 'bdfd5f27c105772682c88ed671683f1bd93f4a3c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../apimatic/unirest-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '6.5.8',
            'version' => '6.5.8.0',
            'reference' => 'a52f0440530b54fa079ce76e8c5d196a42cad981',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'reference' => '67ab6e18aaa14d753cc148911d273f6e6cb6721e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'e4490cabc77465aaee90b20cfc9a770f8c04be6b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-jsonpointer/php-jsonpointer' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '4428f86c6f23846e9faa5a420c4ef14e485b3afb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-jsonpointer/php-jsonpointer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'square/square' => array(
            'pretty_version' => '40.0.0.20250123',
            'version' => '40.0.0.20250123',
            'reference' => '4c8c88afbafb476a3e3f5751c987674874361ca9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../square/square',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
