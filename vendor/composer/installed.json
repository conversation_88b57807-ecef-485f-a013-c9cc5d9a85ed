{"packages": [{"name": "apimatic/core", "version": "0.3.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/apimatic/core-lib-php.git", "reference": "c3eaad6cf0c00b793ce6d9bee8b87176247da582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/apimatic/core-lib-php/zipball/c3eaad6cf0c00b793ce6d9bee8b87176247da582", "reference": "c3eaad6cf0c00b793ce6d9bee8b87176247da582", "shasum": ""}, "require": {"apimatic/core-interfaces": "~0.1.5", "apimatic/jsonmapper": "^3.1.1", "ext-curl": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "php": "^7.2 || ^8.0", "php-jsonpointer/php-jsonpointer": "^3.0.2", "psr/log": "^1.1.4 || ^2.0.0 || ^3.0.0"}, "require-dev": {"phan/phan": "5.4.5", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "squizlabs/php_codesniffer": "^3.5"}, "time": "2025-02-27T06:03:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Core\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Core logic and the utilities for the Apimatic's PHP SDK", "homepage": "https://github.com/apimatic/core-lib-php", "keywords": ["apimatic", "core", "corelib", "php"], "support": {"issues": "https://github.com/apimatic/core-lib-php/issues", "source": "https://github.com/apimatic/core-lib-php/tree/0.3.14"}, "install-path": "../apimatic/core"}, {"name": "apimatic/core-interfaces", "version": "0.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/apimatic/core-interfaces-php.git", "reference": "b4f1bffc8be79584836f70af33c65e097eec155c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/apimatic/core-interfaces-php/zipball/b4f1bffc8be79584836f70af33c65e097eec155c", "reference": "b4f1bffc8be79584836f70af33c65e097eec155c", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2024-05-09T06:32:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"CoreInterfaces\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Definition of the behavior of apimatic/core, apimatic/unirest-php and Apimatic's PHP SDK", "homepage": "https://github.com/apimatic/core-interfaces-php", "keywords": ["apimatic", "core", "corelib", "interface", "php", "unirest"], "support": {"issues": "https://github.com/apimatic/core-interfaces-php/issues", "source": "https://github.com/apimatic/core-interfaces-php/tree/0.1.5"}, "install-path": "../apimatic/core-interfaces"}, {"name": "apimatic/jsonmapper", "version": "3.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/apimatic/jsonmapper.git", "reference": "c6cc21bd56bfe5d5822bbd08f514be465c0b24e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/apimatic/jsonmapper/zipball/c6cc21bd56bfe5d5822bbd08f514be465c0b24e7", "reference": "c6cc21bd56bfe5d5822bbd08f514be465c0b24e7", "shasum": ""}, "require": {"ext-json": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "squizlabs/php_codesniffer": "^3.0.0"}, "time": "2024-11-28T09:15:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"apimatic\\jsonmapper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.netresearch.de/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://apimatic.io/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "support": {"email": "<EMAIL>", "issues": "https://github.com/apimatic/jsonmapper/issues", "source": "https://github.com/apimatic/jsonmapper/tree/3.1.6"}, "install-path": "../apimatic/jsonmapper"}, {"name": "apimatic/unirest-php", "version": "4.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/apimatic/unirest-php.git", "reference": "bdfd5f27c105772682c88ed671683f1bd93f4a3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/apimatic/unirest-php/zipball/bdfd5f27c105772682c88ed671683f1bd93f4a3c", "reference": "bdfd5f27c105772682c88ed671683f1bd93f4a3c", "shasum": ""}, "require": {"apimatic/core-interfaces": "^0.1.0", "ext-curl": "*", "ext-json": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phan/phan": "5.4.2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "squizlabs/php_codesniffer": "^3.5"}, "time": "2025-06-17T09:09:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Unirest\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Mashape", "email": "<EMAIL>", "homepage": "https://www.mashape.com", "role": "Developer"}, {"name": "APIMATIC", "email": "<EMAIL>", "homepage": "https://www.apimatic.io", "role": "Developer"}], "description": "Unirest PHP", "homepage": "https://github.com/apimatic/unirest-php", "keywords": ["client", "curl", "http", "https", "rest"], "support": {"email": "<EMAIL>", "issues": "https://github.com/apimatic/unirest-php/issues", "source": "https://github.com/apimatic/unirest-php/tree/4.0.7"}, "install-path": "../apimatic/unirest-php"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2022-06-20T22:16:07+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "time": "2023-05-21T12:31:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2023-04-17T16:00:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "php-jsonpointer/php-jsonpointer", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/raphaelstolt/php-jsonpointer.git", "reference": "4428f86c6f23846e9faa5a420c4ef14e485b3afb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/raphaelstolt/php-jsonpointer/zipball/4428f86c6f23846e9faa5a420c4ef14e485b3afb", "reference": "4428f86c6f23846e9faa5a420c4ef14e485b3afb", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.11", "phpunit/phpunit": "4.6.*"}, "time": "2016-08-29T08:51:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Rs\\Json": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://raphaelstolt.blogspot.com/"}], "description": "Implementation of JSON Pointer (http://tools.ietf.org/html/rfc6901)", "homepage": "https://github.com/raphaelstolt/php-jsonpointer", "keywords": ["json", "json pointer", "json traversal"], "support": {"issues": "https://github.com/raphaelstolt/php-jsonpointer/issues", "source": "https://github.com/raphaelstolt/php-jsonpointer/tree/master"}, "install-path": "../php-jsonpointer/php-jsonpointer"}, {"name": "psr/http-message", "version": "1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:50:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "square/square", "version": "40.0.0.20250123", "version_normalized": "40.0.0.20250123", "source": {"type": "git", "url": "https://github.com/square/square-php-sdk.git", "reference": "4c8c88afbafb476a3e3f5751c987674874361ca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/square/square-php-sdk/zipball/4c8c88afbafb476a3e3f5751c987674874361ca9", "reference": "4c8c88afbafb476a3e3f5751c987674874361ca9", "shasum": ""}, "require": {"apimatic/core": "~0.3.12", "apimatic/core-interfaces": "~0.1.5", "apimatic/unirest-php": "^4.0.0", "ext-json": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phan/phan": "5.4.5", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "squizlabs/php_codesniffer": "^3.5"}, "time": "2025-01-23T16:01:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Square\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Square Developer Platform", "email": "<EMAIL>", "homepage": "https://squareup.com/developers"}], "description": "Use Square APIs to manage and run business including payment, customer, product, inventory, and employee management.", "homepage": "https://squareup.com/developers", "keywords": ["api", "sdk", "square"], "support": {"issues": "https://github.com/square/square-php-sdk/issues", "source": "https://github.com/square/square-php-sdk/tree/40.0.0.20250123"}, "install-path": "../square/square"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-10T14:38:51+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}], "dev": true, "dev-package-names": []}