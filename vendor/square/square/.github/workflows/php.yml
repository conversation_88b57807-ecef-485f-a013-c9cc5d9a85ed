name: <PERSON><PERSON> Composer

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    env:
      SQUARE_ENVIRONMENT: sandbox
      SQUARE_ACCESS_TOKEN: ${{ secrets.SQUARE_SANDBOX_TOKEN }}

    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: ["7.4", "8.0", "8.1"]
        phpunit-versions: ["8.5.8"]

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          tools: phpunit:${{ matrix.phpunit-versions }}

      - name: Cache Composer packages
        id: composer-cache
        uses: actions/cache@v2
        with:
          path: vendor
          key: ${{ runner.os }}-php-${{ matrix.php-versions }}-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-php-${{ matrix.php-versions }}-
      - name: Install dependencies
        run: composer install --prefer-dist --no-progress

      - name: Run test suite
        run: composer run test

  labeler:
    needs: build
    if: ${{ github.event_name == 'pull_request' }}
    runs-on: ubuntu-latest
    steps:
      - name: automerge-labeler
        uses: fuxingloh/multi-labeler@v1

  automerge:
    needs: labeler
    if: ${{ github.event_name == 'pull_request' }}
    runs-on: ubuntu-latest
    steps:
      - name: automerge
        uses: "pascalgn/automerge-action@v0.14.2"
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          MERGE_LABELS: "automerge,automerge-branch,automerge-author"
