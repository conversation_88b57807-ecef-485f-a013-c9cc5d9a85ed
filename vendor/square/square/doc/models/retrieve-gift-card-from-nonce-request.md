
# Retrieve Gift Card From Nonce Request

A request to retrieve a gift card by using a payment token.

## Structure

`RetrieveGiftCardFromNonceRequest`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `nonce` | `string` | Required | The payment token of the gift card to retrieve. Payment tokens are generated by the<br>Web Payments SDK or In-App Payments SDK.<br>**Constraints**: *Minimum Length*: `1` | getNonce(): string | setNonce(string nonce): void |

## Example (as JSON)

```json
{
  "nonce": "cnon:7783322135245171"
}
```

