
# Subscription Event Info Code

Supported info codes of a subscription event.

## Enumeration

`SubscriptionEventInfoCode`

## Fields

| Name | Description |
|  --- | --- |
| `LOCATION_NOT_ACTIVE` | The location is not active. |
| `LOCATION_CANNOT_ACCEPT_PAYMENT` | The location cannot accept payments. |
| `CUSTOMER_DELETED` | The subscribing customer profile has been deleted. |
| `CUSTOMER_NO_EMAIL` | The subscribing customer does not have an email. |
| `CUSTOMER_NO_NAME` | The subscribing customer does not have a name. |
| `USER_PROVIDED` | User-provided detail. |

