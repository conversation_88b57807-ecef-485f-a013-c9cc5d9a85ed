
# Tender Square Account Details

Represents the details of a tender with `type` `SQUARE_ACCOUNT`.

## Structure

`TenderSquareAccountDetails`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `status` | [`?string(TenderSquareAccountDetailsStatus)`](../../doc/models/tender-square-account-details-status.md) | Optional | - | getStatus(): ?string | setStatus(?string status): void |

## Example (as J<PERSON><PERSON>)

```json
{
  "status": "AUTHORIZED"
}
```

