
# Select Option

## Structure

`SelectOption`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `referenceId` | `string` | Required | The reference id for the option.<br>**Constraints**: *Minimum Length*: `1`, *Maximum Length*: `40` | getReferenceId(): string | setReferenceId(string referenceId): void |
| `title` | `string` | Required | The title text that displays in the select option button.<br>**Constraints**: *Minimum Length*: `1`, *Maximum Length*: `250` | getTitle(): string | setTitle(string title): void |

## Example (as JSON)

```json
{
  "reference_id": "reference_id6",
  "title": "title8"
}
```

