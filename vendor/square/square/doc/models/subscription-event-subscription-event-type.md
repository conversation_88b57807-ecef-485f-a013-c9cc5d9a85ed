
# Subscription Event Subscription Event Type

Supported types of an event occurred to a subscription.

## Enumeration

`SubscriptionEventSubscriptionEventType`

## Fields

| Name | Description |
|  --- | --- |
| `START_SUBSCRIPTION` | The subscription was started. |
| `PLAN_CHANGE` | The subscription plan was changed. |
| `STOP_SUBSCRIPTION` | The subscription was stopped. |
| `DEACTIVATE_SUBSCRIPTION` | The subscription was deactivated |
| `RESUME_SUBSCRIPTION` | The subscription was resumed. |
| `PAUSE_SUBSCRIPTION` | The subscription was paused. |
| `BILLING_ANCHOR_DATE_CHANGED` | The billing anchor date was changed. |

