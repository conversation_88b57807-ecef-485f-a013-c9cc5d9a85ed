
# V1 Device

## Structure

`V1Device`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `id` | `?string` | Optional | The device's Square-issued ID. | getId(): ?string | setId(?string id): void |
| `name` | `?string` | Optional | The device's merchant-specified name. | getName(): ?string | setName(?string name): void |

## Example (as JSON)

```json
{
  "id": "id6",
  "name": "name6"
}
```

