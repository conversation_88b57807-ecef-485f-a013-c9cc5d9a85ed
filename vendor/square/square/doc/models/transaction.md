
# Transaction

Represents a transaction processed with Square, either with the
Connect API or with Square Point of Sale.

The `tenders` field of this object lists all methods of payment used to pay in
the transaction.

## Structure

`Transaction`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `id` | `?string` | Optional | The transaction's unique ID, issued by Square payments servers.<br>**Constraints**: *Maximum Length*: `192` | getId(): ?string | setId(?string id): void |
| `locationId` | `?string` | Optional | The ID of the transaction's associated location.<br>**Constraints**: *Maximum Length*: `50` | getLocationId(): ?string | setLocationId(?string locationId): void |
| `createdAt` | `?string` | Optional | The timestamp for when the transaction was created, in RFC 3339 format.<br>**Constraints**: *Maximum Length*: `32` | getCreatedAt(): ?string | setCreatedAt(?string createdAt): void |
| `tenders` | [`?(Tender[])`](../../doc/models/tender.md) | Optional | The tenders used to pay in the transaction. | getTenders(): ?array | setTenders(?array tenders): void |
| `refunds` | [`?(Refund[])`](../../doc/models/refund.md) | Optional | Refunds that have been applied to any tender in the transaction. | getRefunds(): ?array | setRefunds(?array refunds): void |
| `referenceId` | `?string` | Optional | If the transaction was created with the [Charge](api-endpoint:Transactions-Charge)<br>endpoint, this value is the same as the value provided for the `reference_id`<br>parameter in the request to that endpoint. Otherwise, it is not set.<br>**Constraints**: *Maximum Length*: `40` | getReferenceId(): ?string | setReferenceId(?string referenceId): void |
| `product` | [`?string(TransactionProduct)`](../../doc/models/transaction-product.md) | Optional | Indicates the Square product used to process a transaction. | getProduct(): ?string | setProduct(?string product): void |
| `clientId` | `?string` | Optional | If the transaction was created in the Square Point of Sale app, this value<br>is the ID generated for the transaction by Square Point of Sale.<br><br>This ID has no relationship to the transaction's canonical `id`, which is<br>generated by Square's backend servers. This value is generated for bookkeeping<br>purposes, in case the transaction cannot immediately be completed (for example,<br>if the transaction is processed in offline mode).<br><br>It is not currently possible with the Connect API to perform a transaction<br>lookup by this value.<br>**Constraints**: *Maximum Length*: `192` | getClientId(): ?string | setClientId(?string clientId): void |
| `shippingAddress` | [`?Address`](../../doc/models/address.md) | Optional | Represents a postal address in a country.<br>For more information, see [Working with Addresses](https://developer.squareup.com/docs/build-basics/working-with-addresses). | getShippingAddress(): ?Address | setShippingAddress(?Address shippingAddress): void |
| `orderId` | `?string` | Optional | The order_id is an identifier for the order associated with this transaction, if any.<br>**Constraints**: *Maximum Length*: `192` | getOrderId(): ?string | setOrderId(?string orderId): void |

## Example (as JSON)

```json
{
  "id": "id4",
  "location_id": "location_id8",
  "created_at": "created_at8",
  "tenders": [
    {
      "id": "id8",
      "location_id": "location_id2",
      "transaction_id": "transaction_id6",
      "created_at": "created_at6",
      "note": "note4",
      "type": "THIRD_PARTY_CARD"
    },
    {
      "id": "id8",
      "location_id": "location_id2",
      "transaction_id": "transaction_id6",
      "created_at": "created_at6",
      "note": "note4",
      "type": "THIRD_PARTY_CARD"
    },
    {
      "id": "id8",
      "location_id": "location_id2",
      "transaction_id": "transaction_id6",
      "created_at": "created_at6",
      "note": "note4",
      "type": "THIRD_PARTY_CARD"
    }
  ],
  "refunds": [
    {
      "id": "id8",
      "location_id": "location_id2",
      "transaction_id": "transaction_id6",
      "tender_id": "tender_id6",
      "created_at": "created_at6",
      "reason": "reason4",
      "amount_money": {
        "amount": 186,
        "currency": "AUD"
      },
      "status": "PENDING",
      "processing_fee_money": {
        "amount": 112,
        "currency": "DJF"
      },
      "additional_recipients": [
        {
          "location_id": "location_id0",
          "description": "description6",
          "amount_money": {
            "amount": 186,
            "currency": "AUD"
          },
          "receivable_id": "receivable_id6"
        },
        {
          "location_id": "location_id0",
          "description": "description6",
          "amount_money": {
            "amount": 186,
            "currency": "AUD"
          },
          "receivable_id": "receivable_id6"
        }
      ]
    },
    {
      "id": "id8",
      "location_id": "location_id2",
      "transaction_id": "transaction_id6",
      "tender_id": "tender_id6",
      "created_at": "created_at6",
      "reason": "reason4",
      "amount_money": {
        "amount": 186,
        "currency": "AUD"
      },
      "status": "PENDING",
      "processing_fee_money": {
        "amount": 112,
        "currency": "DJF"
      },
      "additional_recipients": [
        {
          "location_id": "location_id0",
          "description": "description6",
          "amount_money": {
            "amount": 186,
            "currency": "AUD"
          },
          "receivable_id": "receivable_id6"
        },
        {
          "location_id": "location_id0",
          "description": "description6",
          "amount_money": {
            "amount": 186,
            "currency": "AUD"
          },
          "receivable_id": "receivable_id6"
        }
      ]
    },
    {
      "id": "id8",
      "location_id": "location_id2",
      "transaction_id": "transaction_id6",
      "tender_id": "tender_id6",
      "created_at": "created_at6",
      "reason": "reason4",
      "amount_money": {
        "amount": 186,
        "currency": "AUD"
      },
      "status": "PENDING",
      "processing_fee_money": {
        "amount": 112,
        "currency": "DJF"
      },
      "additional_recipients": [
        {
          "location_id": "location_id0",
          "description": "description6",
          "amount_money": {
            "amount": 186,
            "currency": "AUD"
          },
          "receivable_id": "receivable_id6"
        },
        {
          "location_id": "location_id0",
          "description": "description6",
          "amount_money": {
            "amount": 186,
            "currency": "AUD"
          },
          "receivable_id": "receivable_id6"
        }
      ]
    }
  ]
}
```

