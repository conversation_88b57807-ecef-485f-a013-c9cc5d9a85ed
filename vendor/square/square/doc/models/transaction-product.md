
# Transaction Product

Indicates the Square product used to process a transaction.

## Enumeration

`TransactionProduct`

## Fields

| Name | Description |
|  --- | --- |
| `REGISTER` | Square Point of Sale. |
| `EXTERNAL_API` | The Square Connect API. |
| `BILLING` | A Square subscription for one of multiple products. |
| `APPOINTMENTS` | Square Appointments. |
| `INVOICES` | Square Invoices. |
| `ONLINE_STORE` | Square Online Store. |
| `PAYROLL` | Square Payroll. |
| `OTHER` | A Square product that does not match any other value. |

