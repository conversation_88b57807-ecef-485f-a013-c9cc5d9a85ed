
# Tender Card Details

Represents additional details of a tender with `type` `CARD` or `SQUARE_GIFT_CARD`

## Structure

`TenderCardDetails`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `status` | [`?string(TenderCardDetailsStatus)`](../../doc/models/tender-card-details-status.md) | Optional | Indicates the card transaction's current status. | getStatus(): ?string | setStatus(?string status): void |
| `card` | [`?Card`](../../doc/models/card.md) | Optional | Represents the payment details of a card to be used for payments. These<br>details are determined by the payment token generated by Web Payments SDK. | getCard(): ?Card | setCard(?Card card): void |
| `entryMethod` | [`?string(TenderCardDetailsEntryMethod)`](../../doc/models/tender-card-details-entry-method.md) | Optional | Indicates the method used to enter the card's details. | getEntryMethod(): ?string | setEntryMethod(?string entryMethod): void |

## Example (as JSON)

```json
{
  "status": "VOIDED",
  "card": {
    "id": "id6",
    "card_brand": "OTHER_BRAND",
    "last_4": "last_48",
    "exp_month": 228,
    "exp_year": 68
  },
  "entry_method": "CONTACTLESS"
}
```

