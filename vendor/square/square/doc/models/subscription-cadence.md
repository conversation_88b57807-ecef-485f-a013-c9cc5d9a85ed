
# Subscription Cadence

Determines the billing cadence of a [Subscription](../../doc/models/subscription.md)

## Enumeration

`SubscriptionCadence`

## Fields

| Name | Description |
|  --- | --- |
| `DAILY` | Once per day |
| `WEEKLY` | Once per week |
| `EVERY_TWO_WEEKS` | Every two weeks |
| `THIRTY_DAYS` | Once every 30 days |
| `SIXTY_DAYS` | Once every 60 days |
| `NINETY_DAYS` | Once every 90 days |
| `MONTHLY` | Once per month |
| `EVERY_TWO_MONTHS` | Once every two months |
| `QUARTERLY` | Once every three months |
| `EVERY_FOUR_MONTHS` | Once every four months |
| `EVERY_SIX_MONTHS` | Once every six months |
| `ANNUAL` | Once per year |
| `EVERY_TWO_YEARS` | Once every two years |

