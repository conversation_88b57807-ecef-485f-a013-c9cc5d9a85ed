
# Save Card Options

Describes save-card action fields.

## Structure

`SaveCardOptions`

## Fields

| Name | Type | Tags | Description | Getter | Setter |
|  --- | --- | --- | --- | --- | --- |
| `customerId` | `string` | Required | The square-assigned ID of the customer linked to the saved card. | getCustomerId(): string | setCustomerId(string customerId): void |
| `cardId` | `?string` | Optional | The id of the created card-on-file.<br>**Constraints**: *Maximum Length*: `64` | getCardId(): ?string | setCardId(?string cardId): void |
| `referenceId` | `?string` | Optional | An optional user-defined reference ID that can be used to associate<br>this `Card` to another entity in an external system. For example, a customer<br>ID generated by a third-party system.<br>**Constraints**: *Maximum Length*: `128` | getReferenceId(): ?string | setReferenceId(?string referenceId): void |

## Example (as JSO<PERSON>)

```json
{
  "customer_id": "customer_id4",
  "card_id": "card_id8",
  "reference_id": "reference_id6"
}
```

