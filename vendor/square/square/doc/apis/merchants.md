# Merchants

```php
$merchantsApi = $client->getMerchantsApi();
```

## Class Name

`MerchantsApi`

## Methods

* [List Merchants](../../doc/apis/merchants.md#list-merchants)
* [Retrieve Merchant](../../doc/apis/merchants.md#retrieve-merchant)


# List Merchants

Provides details about the merchant associated with a given access token.

The access token used to connect your application to a Square seller is associated
with a single merchant. That means that `ListMerchants` returns a list
with a single `Merchant` object. You can specify your personal access token
to get your own merchant information or specify an OAuth token to get the
information for the merchant that granted your application access.

If you know the merchant ID, you can also use the [RetrieveMerchant](../../doc/apis/merchants.md#retrieve-merchant)
endpoint to retrieve the merchant information.

```php
function listMerchants(?int $cursor = null): ApiResponse
```

## Parameters

| Parameter | Type | Tags | Description |
|  --- | --- | --- | --- |
| `cursor` | `?int` | Query, Optional | The cursor generated by the previous response. |

## Response Type

This method returns a `Square\Utils\ApiResponse` instance. The `getResult()` method on this instance returns the response data which is of type [`ListMerchantsResponse`](../../doc/models/list-merchants-response.md).

## Example Usage

```php
$apiResponse = $merchantsApi->listMerchants();

if ($apiResponse->isSuccess()) {
    $listMerchantsResponse = $apiResponse->getResult();
} else {
    $errors = $apiResponse->getErrors();
}

// Getting more response information
var_dump($apiResponse->getStatusCode());
var_dump($apiResponse->getHeaders());
```


# Retrieve Merchant

Retrieves the `Merchant` object for the given `merchant_id`.

```php
function retrieveMerchant(string $merchantId): ApiResponse
```

## Parameters

| Parameter | Type | Tags | Description |
|  --- | --- | --- | --- |
| `merchantId` | `string` | Template, Required | The ID of the merchant to retrieve. If the string "me" is supplied as the ID,<br>then retrieve the merchant that is currently accessible to this call. |

## Response Type

This method returns a `Square\Utils\ApiResponse` instance. The `getResult()` method on this instance returns the response data which is of type [`RetrieveMerchantResponse`](../../doc/models/retrieve-merchant-response.md).

## Example Usage

```php
$merchantId = 'merchant_id0';

$apiResponse = $merchantsApi->retrieveMerchant($merchantId);

if ($apiResponse->isSuccess()) {
    $retrieveMerchantResponse = $apiResponse->getResult();
} else {
    $errors = $apiResponse->getErrors();
}

// Getting more response information
var_dump($apiResponse->getStatusCode());
var_dump($apiResponse->getHeaders());
```

