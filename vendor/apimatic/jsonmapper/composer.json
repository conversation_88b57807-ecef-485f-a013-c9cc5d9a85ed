{"name": "apimatic/jsonmapper", "description": "Map nested JSON structures onto PHP classes", "license": "OSL-3.0", "autoload": {"psr-4": {"apimatic\\jsonmapper\\": "src/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.netresearch.de/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://apimatic.io/", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/apimatic/jsonmapper/issues"}, "require-dev": {"squizlabs/php_codesniffer": "^3.0.0", "phpunit/phpunit": "^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0"}, "require": {"php": "^5.6 || ^7.0 || ^8.0", "ext-json": "*"}, "scripts": {"test": "phpunit --coverage-text", "test-coverage": "phpunit --path-coverage --coverage-html=CodeCoverage", "lint-fix": "phpcbf --standard=PEAR src/", "lint": "phpcs --standard=PEAR src/"}}