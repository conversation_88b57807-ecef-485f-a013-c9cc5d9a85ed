## What
Write a summary of the changes made in the PR.

## Why
Explain the reasons for the changes.

Closes #

## Type of change
Select multiple if applicable.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause a breaking change)
- [ ] Tests (adds or updates tests)
- [ ] Documentation (adds or updates documentation)
- [ ] Refactor (style improvements, performance improvements, code refactoring)
- [ ] Revert (reverts a commit)
- [ ] CI/Build (adds or updates a script, change in external dependencies)

## Dependency Change
If a new dependency is being added, please ensure that it adheres to the following guideline https://github.com/apimatic/apimatic-codegen/wiki/Policy-of-adding-new-dependencies-in-the-core-libraries

## Breaking change
If the PR is introducing a breaking change, please ensure that it adheres to the following guideline https://github.com/apimatic/apimatic-codegen/wiki/Guidelines-for-maintaining-core-libraries

## Testing
List the steps that were taken to test the changes

## Checklist
- [ ] My code follows the coding conventions
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have added new unit tests
