# Contributing to apimatic/unirest-php

Thank you for your interest in contributing! 🎉 Your contributions help make this project better. We value open-source contributions to this library, please take a few minutes to review this guide before you start.

---

## 💡 How to Contribute

### 📌 1. Reporting Issues

- Search existing issues before opening a new one.
- Use a **descriptive title** and provide clear steps to reproduce.
- Include relevant logs, screenshots, or error messages.

### 🔧 2. Making Changes

#### Step 1: Create a Branch

- Create a new branch from `master`:
  ```sh
  git checkout -b your-feature-name
  ```

#### Step 2: Make Your Changes

- Follow the project's **coding standards**.
- Add **unit tests** if applicable.
- Ensure your changes **do not break existing functionality**.

#### Step 3: Commit Changes

- Use clear and descriptive commit messages:
  ```sh
  git commit -m "feat: Add feature description"
  ```

#### Step 4: Push & Open a PR

- Push your branch:
  ```sh
  git push origin your-feature-name
  ```
- Open a **Pull Request (PR)** on GitHub:
  - Provide a clear **description** of the changes.
  - Mention related **issue numbers**.
  - Request a **review** from maintainers.
  - Make sure your changes clear all the **PR Checks**.

---

## 📜 License

By contributing, you agree that your contributions will be licensed under the project's [LICENSE](LICENSE).

---

💬 **Questions?**  
If you need help, feel free to open issues, we will be happy to help.

Happy Coding! 🚀

