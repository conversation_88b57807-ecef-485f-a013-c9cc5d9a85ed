name: Release To Packagist
run-name: Publishing Package Version ${{ github.event.inputs.Version }}
on:
  workflow_dispatch:
    inputs:
      Version:
        description: "This input field requires version in format: x.y.z, where x => major version, y => minor version and z => patch version"
        required: true
jobs:
  create-release:
    name: Creating release version ${{ github.event.inputs.Version }}
    runs-on: ubuntu-latest
    environment: Production
    steps:
      - uses: actions/checkout@v3

      - name: Create Tag
        id: tag_version
        uses: mathieudutour/github-tag-action@v6.0
        with:
          github_token: ${{ secrets.TAGS_TOKEN }}
          custom_tag: ${{ github.event.inputs.Version }}
          tag_prefix: ""

      - name: Create Release
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ steps.tag_version.outputs.new_tag }}
          name: Release Version ${{ github.event.inputs.Version }}
          body: ${{ steps.tag_version.outputs.changelog }}

      - name: Send slack notification
        id: slack
        uses: slackapi/slack-github-action@v1.25.0
        with:
          channel-id: 'C012YFE3D6D'
          slack-message: "unirest-php release has been triggered!"
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}