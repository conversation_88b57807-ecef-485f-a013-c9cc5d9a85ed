<?php

class Shop_Stripe_Checkout_Payment extends Shop_PaymentType
{
	/**
	 * Returns information about the payment type
	 * Must return array: array(
	 *		'name'=>'Stripe Checkout', 
	 *		'custom_payment_form'=>false,
	 *		'offline'=>false,
	 *		'pay_offline_message'=>null
	 * ).
	 * Use custom_payment_form key to specify a name of a partial to use for building a back-end
	 * payment form. Usually it is needed for forms which ACTION refer outside web services, 
	 * like PayPal Standard. Otherwise override build_payment_form method to build back-end payment
	 * forms.
	 * If the payment type provides a front-end partial (containing the payment form), 
	 * it should be called in following way: payment:name, in lower case, e.g. payment:stripe.checkout
	 *
	 * Set index 'offline' to true to specify that the payments of this type cannot be processed online 
	 * and thus they have no payment form. You may specify a message to display on the payment page
	 * for offline payment type, using 'pay_offline_message' index.
	 *
	 * @return array
	 */
	public function get_info()
	{
		return array(
			'name'=>'Stripe Checkout',
			'custom_payment_form'=>'backend_payment_form.htm',
			'description'=>'Stripe Checkout payment method, with payment form hosted on Stripe server.'
		);
	}

	/**
	 * Builds the payment type administration user interface 
	 * For drop-down and radio fields you should also add methods returning 
	 * options. For example, of you want to have Sizes drop-down:
	 * public function get_sizes_options();
	 * This method should return array with keys corresponding your option identifiers
	 * and values corresponding its titles.
	 * 
	 * @param $host_obj ActiveRecord object to add fields to
	 * @param string $context Form context. In preview mode its value is 'preview'
	 */
	public function build_config_ui($host_obj, $context = null)
	{
		$host_obj->add_field('test_mode', 'Test Mode')->tab('Configuration')->renderAs(frm_onoffswitcher)->comment('Enable this to use Stripe in test mode.', 'above');
		
		$host_obj->add_field('test_secret_key', 'Test Secret Key')->tab('Configuration')->renderAs(frm_text)->comment('Stripe Secret Key for test mode.', 'above')->validation()->fn('trim')->required('Please provide Stripe Secret Key for test mode.');
		$host_obj->add_field('test_publishable_key', 'Test Publishable Key')->tab('Configuration')->renderAs(frm_text)->comment('Stripe Publishable Key for test mode.', 'above')->validation()->fn('trim')->required('Please provide Stripe Publishable Key for test mode.');
		
		$host_obj->add_field('live_secret_key', 'Live Secret Key')->tab('Configuration')->renderAs(frm_text)->comment('Stripe Secret Key for live mode.', 'above')->validation()->fn('trim')->required('Please provide Stripe Secret Key for live mode.');
		$host_obj->add_field('live_publishable_key', 'Live Publishable Key')->tab('Configuration')->renderAs(frm_text)->comment('Stripe Publishable Key for live mode.', 'above')->validation()->fn('trim')->required('Please provide Stripe Publishable Key for live mode.');
		
		$host_obj->add_field('webhook_secret', 'Webhook Secret')->tab('Configuration')->renderAs(frm_text)->comment('Stripe Webhook Secret for verifying webhook signatures. Create this in your Stripe Dashboard under Developers > Webhooks.', 'above')->validation()->fn('trim');
		
		if ($context !== 'preview')
		{
			$host_obj->add_form_partial($host_obj->get_partial_path('hint.htm'))->tab('Configuration');
		}
		
		$host_obj->add_field('cancel_page', 'Cancel Page', 'left')->tab('Configuration')->renderAs(frm_dropdown)->formElementPartial(PATH_APP.'/modules/shop/controllers/partials/_page_selector.htm')->comment('Page which the customer\'s browser is redirected to if payment is cancelled.', 'above')->emptyOption('<please select a page>');
		
		$host_obj->add_field('order_status', 'Order Status', 'right')->tab('Configuration')->renderAs(frm_dropdown)->comment('Select status to assign the order in case of successful payment.', 'above');
		
		$host_obj->add_field('skip_itemized_data', 'Do not submit itemized order information')->tab('Configuration')->renderAs(frm_checkbox)->comment('Enable this option if you don\'t want to submit itemized order information with a transaction. When the option is enabled only the order total amount is submitted to Stripe.', 'above');
	}
	
	public function get_order_status_options($current_key_value = -1)
	{
		if ($current_key_value == -1)
			return Shop_OrderStatus::create()->order('name')->find_all()->as_array('name', 'id');

		return Shop_OrderStatus::create()->find($current_key_value)->name;
	}
	
	public function get_cancel_page_options($current_key_value = -1)
	{
		if ($current_key_value == -1)
			return Cms_Page::create()->order('title')->find_all()->as_array('title', 'id');

		return Cms_Page::create()->find($current_key_value)->title;
	}

	/**
	 * Validates configuration data before it is saved to database
	 * Use host object field_error method to report about errors in data:
	 * $host_obj->field_error('max_weight', 'Max weight should not be less than Min weight');
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 */
	public function validate_config_on_save($host_obj)
	{
		// Validate that API keys have the correct format
		if (!preg_match('/^sk_test_/', $host_obj->test_secret_key))
			$host_obj->field_error('test_secret_key', 'Test Secret Key should start with "sk_test_"');
			
		if (!preg_match('/^pk_test_/', $host_obj->test_publishable_key))
			$host_obj->field_error('test_publishable_key', 'Test Publishable Key should start with "pk_test_"');
			
		if (!preg_match('/^sk_live_/', $host_obj->live_secret_key))
			$host_obj->field_error('live_secret_key', 'Live Secret Key should start with "sk_live_"');
			
		if (!preg_match('/^pk_live_/', $host_obj->live_publishable_key))
			$host_obj->field_error('live_publishable_key', 'Live Publishable Key should start with "pk_live_"');
	}
	
	/**
	 * Validates configuration data after it is loaded from database
	 * Use host object to access fields previously added with build_config_ui method.
	 * You can alter field values if you need
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 */
	public function validate_config_on_load($host_obj)
	{
	}

	/**
	 * Initializes configuration data when the payment method is first created
	 * Use host object to access and set fields previously added with build_config_ui method.
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 */
	public function init_config_data($host_obj)
	{
		$host_obj->test_mode = 1;
	}
	
	/**
	 * Gets the active secret key based on test mode setting
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 * @return string
	 */
	protected function get_secret_key($host_obj)
	{
		return $host_obj->test_mode ? $host_obj->test_secret_key : $host_obj->live_secret_key;
	}
	
	/**
	 * Gets the active publishable key based on test mode setting
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 * @return string
	 */
	protected function get_publishable_key($host_obj)
	{
		return $host_obj->test_mode ? $host_obj->test_publishable_key : $host_obj->live_publishable_key;
	}
	
	/**
	 * Processes payment using passed data
	 * @param array $data Posted payment form data
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 * @param $order Order object
	 */
	public function process_payment_form($data, $host_obj, $order, $back_end = false)
	{
		try
		{
			// Load Stripe PHP SDK - we'll need to include it
			require_once(PATH_APP.'/modules/shop/payment_types/shop_stripe_checkout_payment/stripe-php/init.php');
			
			// Set API key
			\Stripe\Stripe::setApiKey($this->get_secret_key($host_obj));
			
			// Build line items for Stripe
			$line_items = array();
			
			if (!$host_obj->skip_itemized_data)
			{
				// Add order items
				foreach ($order->items as $item)
				{
					$line_items[] = [
						'price_data' => [
							'currency' => strtolower(Shop_CurrencySettings::get()->code),
							'product_data' => [
								'name' => $item->output_product_name(true, true),
							],
							'unit_amount' => round($item->unit_total_price * 100), // Stripe requires amounts in cents
						],
						'quantity' => $item->quantity,
					];
				}
				
				// Add shipping as a line item if applicable
				if ($order->get_shipping_quote_discounted() > 0)
				{
					$line_items[] = [
						'price_data' => [
							'currency' => strtolower(Shop_CurrencySettings::get()->code),
							'product_data' => [
								'name' => 'Shipping',
							],
							'unit_amount' => round($order->get_shipping_quote_discounted() * 100),
						],
						'quantity' => 1,
					];
					
					// Add shipping tax as a separate line item if applicable
					if ($order->shipping_tax > 0)
					{
						$line_items[] = [
							'price_data' => [
								'currency' => strtolower(Shop_CurrencySettings::get()->code),
								'product_data' => [
									'name' => 'Shipping Tax',
								],
								'unit_amount' => round($order->shipping_tax * 100),
							],
							'quantity' => 1,
						];
					}
				}
				
				// Add tax as a separate line item if applicable
				if ($order->goods_tax > 0)
				{
					$line_items[] = [
						'price_data' => [
							'currency' => strtolower(Shop_CurrencySettings::get()->code),
							'product_data' => [
								'name' => 'Tax',
							],
							'unit_amount' => round($order->goods_tax * 100),
						],
						'quantity' => 1,
					];
				}
			}
			else
			{
				// Just add a single line item with the total
				$line_items[] = [
					'price_data' => [
						'currency' => strtolower(Shop_CurrencySettings::get()->code),
						'product_data' => [
							'name' => 'Order #'.$order->get_order_reference(),
						],
						'unit_amount' => round($order->total * 100), // Stripe requires amounts in cents
					],
					'quantity' => 1,
				];
			}
			
			// Define success and cancel URLs
			$success_url = Phpr::$request->getRootUrl().root_url('/ls_stripe_return/'.$order->order_hash);
			
			$cancel_page = $this->get_cancel_page($host_obj);
			$cancel_url = '';
			if ($cancel_page)
			{
				$cancel_url = Phpr::$request->getRootUrl().root_url($cancel_page->url);
				if ($cancel_page->action_reference == 'shop:pay')
					$cancel_url .= '/'.$order->order_hash;
				elseif ($cancel_page->action_reference == 'shop:order')
					$cancel_url .= '/'.$order->id;
			}
			else
			{
				// Default cancel URL if none specified
				$cancel_url = Phpr::$request->getRootUrl().root_url('/shop/checkout');
			}
			
			// Create Checkout Session
			$checkout_session = \Stripe\Checkout\Session::create([
				'payment_method_types' => ['card'],
				'line_items' => $line_items,
				'mode' => 'payment',
				'success_url' => $success_url,
				'cancel_url' => $cancel_url,
				'client_reference_id' => $order->order_hash,
				'customer_email' => $order->billing_email,
				'metadata' => [
					'order_id' => $order->id,
					'order_reference' => $order->get_order_reference()
				],
			]);
			
			// Store the session ID in the order payment transaction
			$this->create_transaction_record($host_obj, $order, $checkout_session->id, 'Pending', 'pending');
			
			// Redirect to Stripe Checkout
			Phpr::$response->redirect($checkout_session->url);
		}
		catch (Exception $ex)
		{
			$this->log_payment_attempt($order, $ex->getMessage(), 0, $data);
			throw new Phpr_ApplicationException('Error processing payment: '.$ex->getMessage());
		}
	}
	
	protected function get_cancel_page($host_obj)
	{
		$cancel_page = $host_obj->cancel_page;
		$page_info = Cms_PageReference::get_page_info($host_obj, 'cancel_page', $host_obj->cancel_page);
		if (is_object($page_info))
			$cancel_page = $page_info->page_id;

		if (!$cancel_page)
			return null;

		return Cms_Page::create()->find($cancel_page);
	}

	/**
	 * Registers a hidden page with specific URL. Use this method for cases when you 
	 * need to have a hidden landing page for a specific payment gateway. For example, 
	 * PayPal needs a landing page for the auto-return feature.
	 * Important! Payment module access point names should have the ls_ prefix.
	 * @return array Returns an array containing page URLs and methods to call for each URL:
	 * return array('ls_paypal_autoreturn'=>'process_paypal_autoreturn'). The processing methods must be declared 
	 * in the payment type class. Processing methods must accept one parameter - an array of URL segments 
	 * following the access point. For example, if URL is /ls_paypal_autoreturn/1234 an array with single
	 * value '1234' will be passed to process_paypal_autoreturn method 
	 */
	public function register_access_points()
	{
		return array(
			'ls_stripe_return' => 'process_stripe_return',
			'ls_stripe_webhook' => 'process_stripe_webhook'
		);
	}
	
	/**
	 * Process the return from Stripe Checkout after payment
	 */
	public function process_stripe_return($params)
	{
		try
		{
			$order = null;
			
			// Find order by hash
			$order_hash = array_key_exists(0, $params) ? $params[0] : null;
			if (!$order_hash)
				throw new Phpr_ApplicationException('Order not found');
			
			$order = Shop_Order::create()->find_by_order_hash($order_hash);
			if (!$order)
				throw new Phpr_ApplicationException('Order not found.');
			
			if (!$order->payment_method)
				throw new Phpr_ApplicationException('Payment method not found.');
			
			// For return URL, we don't automatically mark order as paid
			// We rely on the webhook for that, but we redirect to success page
			
			$return_page = $order->payment_method->receipt_page;
			if ($return_page)
				Phpr::$response->redirect(root_url($return_page->url.'/'.$order->order_hash).'?utm_nooverride=1');
			else 
				throw new Phpr_ApplicationException('Stripe Checkout Receipt page is not found.');
		}
		catch (Exception $ex)
		{
			if ($order)
				$this->log_payment_attempt($order, $ex->getMessage(), 0, array(), Phpr::$request->get_fields);
			
			throw new Phpr_ApplicationException($ex->getMessage());
		}
	}
	
	/**
	 * Process webhooks from Stripe
	 */
	public function process_stripe_webhook($params)
	{
		try
		{
			// Get the webhook payload
			$payload = @file_get_contents('php://input');
			$sig_header = isset($_SERVER['HTTP_STRIPE_SIGNATURE']) ? $_SERVER['HTTP_STRIPE_SIGNATURE'] : '';
			$event = null;
			
			// Find the payment method to get the webhook secret
			$payment_method = Shop_PaymentMethod::create()->where('class_name=?', 'Shop_Stripe_Checkout_Payment')->find();
			if (!$payment_method)
				throw new Phpr_ApplicationException('Payment method not found.');
			
			$payment_method->define_form_fields();
			
			// Load Stripe PHP SDK
			require_once(PATH_APP.'/modules/shop/payment_types/shop_stripe_checkout_payment/stripe-php/init.php');
			
			// Verify webhook signature if secret is set
			if ($payment_method->webhook_secret && $sig_header)
			{
				try
				{
					$event = \Stripe\Webhook::constructEvent(
						$payload, $sig_header, $payment_method->webhook_secret
					);
				}
				catch (\UnexpectedValueException $e)
				{
					// Invalid payload
					http_response_code(400);
					exit();
				}
				catch (\Stripe\Exception\SignatureVerificationException $e)
				{
					// Invalid signature
					http_response_code(400);
					exit();
				}
			}
			else
			{
				// If no webhook secret, just decode the payload
				$event = json_decode($payload);
			}
			
			// Handle the event
			if ($event->type == 'checkout.session.completed')
			{
				$session = $event->data->object;
				
				// Find order by client_reference_id (order_hash)
				$order_hash = $session->client_reference_id;
				$order = Shop_Order::create()->find_by_order_hash($order_hash);
				
				if (!$order)
					throw new Phpr_ApplicationException('Order not found: ' . $order_hash);
				
				if (!$order->payment_method)
					throw new Phpr_ApplicationException('Payment method not found for order: ' . $order_hash);
				
				// Mark the order as paid if it's not already
				if (!$order->payment_processed(false))
				{
					if ($order->set_payment_processed())
					{
						Shop_OrderStatusLog::create_record($order->payment_method->order_status, $order);
						$this->log_payment_attempt($order, 'Successful payment via Stripe Checkout', 1, array(), array('event' => $event->type));
						
						// Update the transaction status
						$this->update_transaction_status($order->payment_method, $order, $session->payment_intent, 'Paid', 'paid');
					}
				}
			}
			
			http_response_code(200);
		}
		catch (Exception $ex)
		{
			// Log error but return 200 to Stripe
			Phpr::$events->fireEvent('onLogStripeError', $ex->getMessage());
			http_response_code(200);
		}
	}
	
	/**
	 * This function is called before a CMS page deletion.
	 * Use this method to check whether the payment method
	 * references a page. If so, throw Phpr_ApplicationException 
	 * with explanation why the page cannot be deleted.
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 * @param Cms_Page $page Specifies a page to be deleted
	 */
	public function page_deletion_check($host_obj, $page)
	{
		if ($host_obj->cancel_page == $page->id)
			throw new Phpr_ApplicationException('Page cannot be deleted because it is used in Stripe Checkout payment method as a cancel page.');
	}
	
	/**
	 * This function is called before an order status deletion.
	 * Use this method to check whether the payment method
	 * references an order status. If so, throw Phpr_ApplicationException 
	 * with explanation why the status cannot be deleted.
	 * @param $host_obj ActiveRecord object containing configuration fields values
	 * @param Shop_OrderStatus $status Specifies a status to be deleted
	 */
	public function status_deletion_check($host_obj, $status)
	{
		if ($host_obj->order_status == $status->id)
			throw new Phpr_ApplicationException('Status cannot be deleted because it is used in Stripe Checkout payment method.');
	}
	
	public function extend_transaction_preview($payment_method_obj, $controller, $transaction)
	{
		$payment_method_obj->load_xml_data();
		$controller->viewData['transaction_id'] = $transaction->transaction_id;
		
		if ($payment_method_obj->test_mode)
			$controller->viewData['url'] = "https://dashboard.stripe.com/test/payments/";
		else
			$controller->viewData['url'] = "https://dashboard.stripe.com/payments/";
			
		$controller->renderPartial(PATH_APP.'/modules/shop/payment_types/shop_stripe_checkout_payment/_payment_transaction.htm');
	}

	/**
	 * Creates a transaction record
	 */
	protected function create_transaction_record($host_obj, $order, $transaction_id, $status_text, $status_code)
	{
		$record = Shop_PaymentTransaction::create();
		$record->order_id = $order->id;
		$record->payment_method_id = $host_obj->id;
		$record->transaction_id = $transaction_id;
		$record->transaction_status = $status_text;
		$record->transaction_status_code = $status_code;
		$record->save();
		
		return $record;
	}

	/**
	 * Updates a transaction status
	 */
	public function update_transaction_status($host_obj, $order, $transaction_id, $status_text, $status_code, $data = NULL)
	{
		$record = Shop_PaymentTransaction::create()->where('order_id=?', $order->id)->where('payment_method_id=?', $host_obj->id)->find();
		
		if (!$record) {
			return $this->create_transaction_record($host_obj, $order, $transaction_id, $status_text, $status_code);
		}
		
		$record->transaction_id = $transaction_id;
		$record->transaction_status = $status_text;
		$record->transaction_status_code = $status_code;
		$record->save();
		
		return $record;
	}
}
?>

