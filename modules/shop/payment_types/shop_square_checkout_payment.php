<?php
use Square\SquareClient;
use Square\Models\CreatePaymentLinkRequest;
use Square\Models\Order;
use Square\Models\OrderLineItem;
use Square\Models\OrderLineItemTax;
use Square\Models\Money;
use Square\Models\CheckoutOptions;
use Square\Utils\WebhooksHelper;

class Shop_Square_Checkout_Payment extends Shop_PaymentType
{
    public function get_info()
    {
        return [
            'name' => 'Square Checkout',
            'custom_payment_form' => 'backend_payment_form.htm',
            'description' => 'Square Checkout with full webhook support.'
        ];
    }

    public function build_config_ui($host_obj, $context = null)
    {
        $host_obj->add_field('test_mode', 'Sandbox Mode')->tab('Configuration')->renderAs(frm_onoffswitcher);
        $host_obj->add_field('access_token', 'Access Token')->tab('Configuration')->renderAs(frm_text)
            ->validation()->fn('trim')->required('Provide your Square access token.');
        $host_obj->add_field('location_id', 'Location ID')->tab('Configuration')->renderAs(frm_text)
            ->validation()->fn('trim')->required('Provide your Square location ID.');
        $host_obj->add_field('webhook_signature_key', 'Webhook Signature Key')->tab('Configuration')->renderAs(frm_text)
            ->validation()->fn('trim')->required('Provide your webhook signature key.');
        $host_obj->add_field('cancel_page', 'Cancel Page')->tab('Configuration')
            ->renderAs(frm_dropdown)
            ->formElementPartial(PATH_APP.'/modules/shop/controllers/partials/_page_selector.htm')
            ->emptyOption('<please select>');
        $host_obj->add_field('order_status', 'Order Status')->tab('Configuration')->renderAs(frm_dropdown);
    }

    public function get_order_status_options($k = -1) { return Shop_OrderStatus::create()->order('name')->find_all()->as_array('name', 'id'); }
    public function get_cancel_page_options($k = -1) { return Cms_Page::create()->order('title')->find_all()->as_array('title', 'id'); }

    public function validate_config_on_save($host_obj)
    {
        if (!$host_obj->access_token) $host_obj->field_error('access_token','Access token is required.');
        if (!$host_obj->location_id) $host_obj->field_error('location_id','Location ID is required.');
        if (!$host_obj->webhook_signature_key) $host_obj->field_error('webhook_signature_key','Webhook signature key is required.');
    }

    public function init_config_data($host_obj) { $host_obj->test_mode = 1; }

    protected function get_environment($host_obj) { return $host_obj->test_mode ? 'sandbox' : 'production'; }

    public function process_payment_form($data, $host_obj, $order, $back_end = false)
    {
        require_once PATH_APP.'/vendor/autoload.php';
        
        $client = new SquareClient([
            'accessToken' => $host_obj->access_token,
            'environment' => $this->get_environment($host_obj)
        ]);

        // Create line items for the order
        $lineItems = [];
        
        foreach ($order->items as $item) {
            $money = new Money();
            $money->setAmount(round($item->unit_total_price * 100));
            $money->setCurrency(strtoupper(Shop_CurrencySettings::get()->code));

            $lineItem = new OrderLineItem((string)$item->quantity);
            $lineItem->setName((string)$item->product_name);
            $lineItem->setBasePriceMoney($money);

            $lineItems[] = $lineItem;

        }

        // Add shipping as a line item if applicable
        $shipping = $order->get_shipping_quote_discounted();
        if ($shipping > 0) {
            $money = new Money();
            $money->setAmount(round($shipping * 100));
            $money->setCurrency(strtoupper(Shop_CurrencySettings::get()->code));

            $shippingLineItem = new OrderLineItem('1');
            $shippingLineItem->setName('Shipping');
            $shippingLineItem->setBasePriceMoney($money);

            $lineItems[] = $shippingLineItem;
        }

        // Create the order with line items
        $squareOrder = new Order($host_obj->location_id);
        $squareOrder->setLineItems($lineItems);

        // Set order reference so webhook can identify the LemonStand order
        $squareOrder->setReferenceId($order->order_hash);

        // Add tax if applicable - using the proper tax API approach
        if ($order->goods_tax > 0) {
            // Create a tax that applies to the entire order
            $taxUid = 'tax_' . uniqid();
            $taxPercentage = ($order->goods_tax / ($order->subtotal - $order->discount_total)) * 100;
            
            $tax = new OrderLineItemTax();
            $tax->setUid($taxUid);
            $tax->setName('Tax');
            $tax->setPercentage((string)round($taxPercentage, 2));
            $tax->setScope('ORDER'); // Apply to the entire order

            $squareOrder->setTaxes([$tax]);
        }
        
        // Create the payment link request
        $idempotencyKey = uniqid($order->order_hash . '-');
        
        // Create checkout options
        $checkoutOptions = new CheckoutOptions();
        $checkoutOptions->setRedirectUrl($this->get_success_url($order));
        $checkoutOptions->setMerchantSupportEmail(System_EmailParams::get()->sender_email);
        $checkoutOptions->setAskForShippingAddress(false);

        // Set cancel URL if configured (don't overwrite redirect URL)
        if ($host_obj->cancel_page) {
            $cancelUrl = $this->get_cancel_page_url($host_obj, $order);
            // Note: Square doesn't have a separate cancel URL in CheckoutOptions
            // The redirect URL handles both success and cancel scenarios
        }
        
        // Create the request
        $body = new CreatePaymentLinkRequest();
        $body->setIdempotencyKey($idempotencyKey);
        $body->setOrder($squareOrder);
        $body->setCheckoutOptions($checkoutOptions);

        // Try the payment
        try {
            $resp = $client->getCheckoutApi()->createPaymentLink($body);
            if ($resp->isSuccess()) {
                $paymentLink = $resp->getResult()->getPaymentLink();
                $link = $paymentLink->getUrl();
                $this->create_transaction_record($host_obj, $order, $idempotencyKey, 'Pending', 'pending');
                Phpr::$response->redirect($link);
            } else {
                throw new Exception(json_encode($resp->getErrors()));
            }
        }
        catch (Exception $ex) {
            traceLog('Square Payment Error: '.$ex->getMessage());
            $this->log_payment_attempt(
                $order, 
                $ex->getMessage(), 
                0, 
                $data, 
                array('errors' => isset($resp) ? $resp->getErrors() : []), 
                json_encode(isset($resp) ? $resp->getErrors() : [])
            );
            throw new Phpr_ApplicationException('Error: ' . $ex->getMessage());
        }
    }

    protected function get_success_url($order)
    {
        return Phpr::$request->getRootUrl().root_url('/ls_square_return/'.$order->order_hash);
    }

    protected function get_cancel_page_url($host_obj, $order)
    {
        $page = Cms_Page::create()->find($host_obj->cancel_page);
        if ($page) return Phpr::$request->getRootUrl().root_url($page->url.($page->action_reference=='shop:pay' ? '/'.$order->order_hash : '/'.$order->id));
        return Phpr::$request->getRootUrl().root_url('/shop/checkout');
    }

    public function register_access_points()
    {
        return [
            'ls_square_return' => 'process_square_return',
            'ls_square_webhook' => 'process_square_webhook',
            'ls_square_webhook_test' => 'process_square_webhook_test'
        ];
    }

    public function process_square_return($params)
    {
        try {
            $order_hash = array_shift($params);
            if (!$order_hash) {
                throw new Phpr_ApplicationException('Order hash not found');
            }

            $order = Shop_Order::create()->find_by_order_hash($order_hash);
            if (!$order) {
                throw new Phpr_ApplicationException('Order not found');
            }

            if (!$order->payment_method) {
                throw new Phpr_ApplicationException('Payment method not found');
            }

            // Check if payment was successful (this will be confirmed by webhook)
            // For now, just redirect to receipt page - webhook will handle payment processing
            $receipt_page = $order->payment_method->receipt_page;
            if ($receipt_page) {
                Phpr::$response->redirect(root_url($receipt_page->url.'/'.$order_hash));
            } else {
                throw new Phpr_ApplicationException('Square Checkout Receipt page is not found.');
            }
        }
        catch (Exception $ex) {
            if (isset($order)) {
                $this->log_payment_attempt($order, $ex->getMessage(), 0, array(), Phpr::$request->get_fields);
            }
            throw new Phpr_ApplicationException($ex->getMessage());
        }
    }

    /**
     * Test webhook handler without signature validation for debugging
     */
    public function process_square_webhook_test($params)
    {
        $body = file_get_contents('php://input');
        traceLog('Square Test Webhook Received: ' . $body);

        try {
            $event = json_decode($body);
            traceLog('Square Test Webhook Event Type: ' . ($event->type ?? 'unknown'));
            traceLog('Square Test Webhook Full Data: ' . json_encode($event));

            http_response_code(200);
            echo "Test webhook received and logged";
        }
        catch (Exception $ex) {
            traceLog('Square Test Webhook Error: ' . $ex->getMessage());
            http_response_code(200);
            echo "Test webhook error: " . $ex->getMessage();
        }
    }

    public function process_square_webhook($params)
    {
        // Log all webhook attempts for debugging
        $body = file_get_contents('php://input');
        $headers = getallheaders();
        traceLog('Square Webhook Received - Body: ' . substr($body, 0, 500) . '...');
        traceLog('Square Webhook Headers: ' . json_encode($headers));

        try {
            // Raw payload & signature
            $sig = $_SERVER['HTTP_X_SQUARE_HMACSHA256_SIGNATURE'] ?? '';
            $url = Phpr::$request->getRootUrl().'/ls_square_webhook';

            // Get the first Square payment method to access webhook signature key
            $payment_method = Shop_PaymentMethod::create()->where('class_name=?', 'Shop_Square_Checkout_Payment')->find();
            if (!$payment_method) {
                traceLog('Square payment method not found in database');
                throw new Phpr_ApplicationException('Square payment method not found');
            }

            traceLog('Using webhook signature key: ' . substr($payment_method->webhook_signature_key, 0, 10) . '...');

            // Validate webhook signature
            if (!WebhooksHelper::isValidWebhookEventSignature($body, $sig, $payment_method->webhook_signature_key, $url)) {
                traceLog('Invalid webhook signature - Expected URL: ' . $url);
                http_response_code(403);
                exit;
            }

            $event = json_decode($body);
            $eid = $event->event_id ?? '';

            // Log webhook event for debugging
            traceLog('Square Webhook Event: ' . ($event->type ?? 'unknown') . ' - Event ID: ' . $eid);
            traceLog('Square Webhook Full Event Data: ' . json_encode($event));

            // Check if event has already been processed to prevent duplicate processing
            if (!$this->is_event_processed($eid)) {
                $this->mark_event_processed($eid);
                $type = $event->type;

                traceLog('Processing new webhook event: ' . $type);

                // Handle payment completion events
                // Square Checkout might send different events, so let's be more flexible
                if (in_array($type, ['payment.created', 'payment.updated', 'order.updated', 'order.created'])) {
                    traceLog('Handling payment/order event: ' . $type);

                    // Try to get payment data from different possible locations
                    $payment = null;
                    $order_data = null;

                    // Check the actual webhook structure from the logs
                    if (isset($event->data->object->payment)) {
                        $payment = $event->data->object->payment;
                        traceLog('Found payment data in event->data->object->payment');
                    }

                    if (isset($event->data->object->order_created)) {
                        $order_data = $event->data->object->order_created;
                        traceLog('Found order_created data in event');
                    } elseif (isset($event->data->object->order_updated)) {
                        $order_data = $event->data->object->order_updated;
                        traceLog('Found order_updated data in event');
                    }

                    // If we have order data but no payment, check if order has payment info
                    if (!$payment && $order_data) {
                        $payment = $order_data;
                        traceLog('Using order data as payment data');
                    }

                    // Check if payment/order is completed
                    $is_completed = false;
                    $order_hash = null;
                    $transaction_id = null;

                    // Try different ways to determine if payment is complete
                    if (isset($payment->status) && $payment->status === 'COMPLETED') {
                        $is_completed = true;
                        $transaction_id = $payment->id ?? null;
                        traceLog('Payment status is COMPLETED');
                    } elseif (isset($order_data->state) && $order_data->state === 'COMPLETED') {
                        $is_completed = true;
                        $transaction_id = $order_data->id ?? null;
                        traceLog('Order state is COMPLETED');
                    }

                    // Try different ways to get the order hash
                    if (isset($event->data->object->order->reference_id)) {
                        $order_hash = $event->data->object->order->reference_id;
                    } elseif (isset($order_data->reference_id)) {
                        $order_hash = $order_data->reference_id;
                    } elseif (isset($payment->order_id)) {
                        // We have Square order_id, need to fetch the order to get reference_id
                        $square_order_id = $payment->order_id;
                        traceLog('Found Square order_id in payment: ' . $square_order_id);

                        // Get the order details from Square to find our reference_id
                        $order_hash = $this->get_order_reference_from_square($square_order_id);
                        traceLog('Retrieved order hash from Square: ' . ($order_hash ?? 'none'));
                    }

                    traceLog('Order hash found: ' . ($order_hash ?? 'none'));
                    traceLog('Is completed: ' . ($is_completed ? 'yes' : 'no'));

                    if ($is_completed && $order_hash) {
                        $order = Shop_Order::create()->find_by_order_hash($order_hash);

                        if (!$order) {
                            throw new Phpr_ApplicationException('Order not found: ' . $order_hash);
                        }

                        if (!$order->payment_method) {
                            throw new Phpr_ApplicationException('Payment method not found for order: ' . $order_hash);
                        }

                        // Mark the order as paid if it's not already
                        if (!$order->payment_processed(false)) {
                            traceLog('Processing payment for order: ' . $order_hash);
                            if ($order->set_payment_processed()) {
                                Shop_OrderStatusLog::create_record($order->payment_method->order_status, $order);
                                $this->log_payment_attempt($order, 'Successful payment via Square Checkout', 1, array(), array('event' => $event->type));

                                // Update the transaction status
                                $this->update_transaction_status($order->payment_method, $order, $transaction_id, 'Paid', 'paid');
                                traceLog('Payment processed successfully for order: ' . $order_hash);
                            }
                        } else {
                            traceLog('Order already processed: ' . $order_hash);
                        }
                    } else {
                        if (!$order_hash) {
                            traceLog('No order hash found in webhook data');
                        }
                        if (!$is_completed) {
                            traceLog('Payment/Order not completed yet');
                        }
                    }
                }
            }

            http_response_code(200);
        }
        catch (Exception $ex) {
            // Log error but return 200 to Square to prevent retries
            traceLog('Square Webhook Error: ' . $ex->getMessage());
            http_response_code(200);
        }
    }

    protected function is_event_processed($eid)
    {
        return Shop_Meta::create()
            ->where('group=?','square_webhook')
            ->where('key=?',$eid)
            ->count() > 0;
    }

    protected function mark_event_processed($eid)
    {
        Shop_Meta::create([
            'group' => 'square_webhook',
            'key' => $eid,
            'value' => time()
        ])->save();
    }

    protected function create_transaction_record($h,$o,$tid,$st,$code)
    {
        return Shop_PaymentTransaction::create([
            'order_id' => $o->id,
            'payment_method_id' => $h->id,
            'transaction_id' => $tid,
            'transaction_status' => $st,
            'transaction_status_code' => $code
        ])->save();
    }

    /**
     * Updates a transaction status
     */
    public function update_transaction_status($host_obj, $order, $transaction_id, $status_text, $status_code, $data = NULL)
    {
        $record = Shop_PaymentTransaction::create()->where('order_id=?', $order->id)->where('payment_method_id=?', $host_obj->id)->find();

        if (!$record) {
            return $this->create_transaction_record($host_obj, $order, $transaction_id, $status_text, $status_code);
        }

        $record->transaction_id = $transaction_id;
        $record->transaction_status = $status_text;
        $record->transaction_status_code = $status_code;
        $record->save();

        return $record;
    }

    /**
     * Get order reference_id from Square using order_id
     */
    protected function get_order_reference_from_square($square_order_id)
    {
        try {
            require_once PATH_APP.'/vendor/autoload.php';

            // Get the first Square payment method to access configuration
            $payment_method = Shop_PaymentMethod::create()->where('class_name=?', 'Shop_Square_Checkout_Payment')->find();
            if (!$payment_method) {
                traceLog('Square payment method not found for order lookup');
                return null;
            }

            $client = new SquareClient([
                'accessToken' => $payment_method->access_token,
                'environment' => $payment_method->test_mode ? 'sandbox' : 'production'
            ]);

            $ordersApi = $client->getOrdersApi();
            $response = $ordersApi->retrieveOrder($square_order_id);

            if ($response->isSuccess()) {
                $order = $response->getResult()->getOrder();
                $reference_id = $order->getReferenceId();
                traceLog('Successfully retrieved reference_id from Square: ' . ($reference_id ?? 'none'));
                return $reference_id;
            } else {
                traceLog('Failed to retrieve order from Square: ' . json_encode($response->getErrors()));
                return null;
            }
        } catch (Exception $ex) {
            traceLog('Exception retrieving order from Square: ' . $ex->getMessage());
            return null;
        }
    }
}
